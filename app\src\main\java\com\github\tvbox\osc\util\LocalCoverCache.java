package com.github.tvbox.osc.util;

import android.text.TextUtils;

import java.io.File;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 本地封面缓存工具类
 * 用于缓存本地封面文件的存在性检查，避免重复的文件系统操作
 */
public class LocalCoverCache {
    
    private static final String TAG = "LocalCoverCache";
    
    // 缓存文件存在性的Map，key为文件路径，value为是否存在
    private static final ConcurrentHashMap<String, Boolean> fileExistsCache = new ConcurrentHashMap<>();
    
    // 缓存最后修改时间，用于检测文件变化
    private static final ConcurrentHashMap<String, Long> lastModifiedCache = new ConcurrentHashMap<>();
    
    // 缓存大小限制，避免内存泄漏
    private static final int MAX_CACHE_SIZE = 1000;
    
    /**
     * 检查本地封面文件是否存在（带缓存）
     * @param filePath 文件路径
     * @return 文件是否存在
     */
    public static boolean exists(String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return false;
        }
        
        // 检查缓存
        Boolean cached = fileExistsCache.get(filePath);
        Long cachedModified = lastModifiedCache.get(filePath);
        
        File file = new File(filePath);
        long currentModified = file.lastModified();
        
        // 如果缓存存在且文件修改时间没变，直接返回缓存结果
        if (cached != null && cachedModified != null && cachedModified == currentModified) {
            return cached;
        }
        
        // 检查实际文件存在性
        boolean exists = file.exists() && file.isFile() && file.length() > 0;
        
        // 更新缓存
        updateCache(filePath, exists, currentModified);
        
        return exists;
    }
    
    /**
     * 获取本地封面文件路径（如果存在）
     * @param dirPath 影片目录路径
     * @return 封面文件路径，如果不存在返回null
     */
    public static String getCoverPath(String dirPath) {
        if (TextUtils.isEmpty(dirPath)) {
            return null;
        }
        
        String coverPath = dirPath + File.separator + LocalDataHelper.COVER_FILE_NAME;
        return exists(coverPath) ? coverPath : null;
    }
    
    /**
     * 获取带file://前缀的封面URI
     * @param dirPath 影片目录路径
     * @return 封面URI，如果不存在返回null
     */
    public static String getCoverUri(String dirPath) {
        String coverPath = getCoverPath(dirPath);
        return coverPath != null ? "file://" + coverPath : null;
    }
    
    /**
     * 预加载指定目录下的封面文件缓存
     * @param rootPath 根目录路径
     */
    public static void preloadCache(String rootPath) {
        if (TextUtils.isEmpty(rootPath)) {
            return;
        }
        
        File rootDir = new File(rootPath);
        if (!rootDir.exists() || !rootDir.isDirectory()) {
            return;
        }
        
        File[] subDirs = rootDir.listFiles();
        if (subDirs == null) {
            return;
        }
        
        for (File subDir : subDirs) {
            if (subDir.isDirectory()) {
                String coverPath = subDir.getAbsolutePath() + File.separator + LocalDataHelper.COVER_FILE_NAME;
                // 预加载到缓存中
                exists(coverPath);
            }
        }
    }
    
    /**
     * 更新缓存
     */
    private static void updateCache(String filePath, boolean exists, long lastModified) {
        // 如果缓存过大，清理一部分
        if (fileExistsCache.size() >= MAX_CACHE_SIZE) {
            clearOldCache();
        }
        
        fileExistsCache.put(filePath, exists);
        lastModifiedCache.put(filePath, lastModified);
    }
    
    /**
     * 清理旧的缓存条目
     */
    private static void clearOldCache() {
        // 简单策略：清理一半的缓存
        int targetSize = MAX_CACHE_SIZE / 2;
        int currentSize = fileExistsCache.size();
        int toRemove = currentSize - targetSize;
        
        if (toRemove > 0) {
            String[] keys = fileExistsCache.keySet().toArray(new String[0]);
            for (int i = 0; i < toRemove && i < keys.length; i++) {
                String key = keys[i];
                fileExistsCache.remove(key);
                lastModifiedCache.remove(key);
            }
        }
    }
    
    /**
     * 清空所有缓存
     */
    public static void clearCache() {
        fileExistsCache.clear();
        lastModifiedCache.clear();
    }
    
    /**
     * 移除指定文件的缓存
     */
    public static void removeFromCache(String filePath) {
        if (!TextUtils.isEmpty(filePath)) {
            fileExistsCache.remove(filePath);
            lastModifiedCache.remove(filePath);
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    public static String getCacheStats() {
        return String.format("LocalCoverCache: %d files cached, %d with timestamps", 
                fileExistsCache.size(), lastModifiedCache.size());
    }
}
