package com.github.tvbox.osc.ui.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Toast;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.github.tvbox.osc.R;
import com.github.tvbox.osc.base.BaseLazyFragment;
import com.github.tvbox.osc.bean.Movie;
import com.github.tvbox.osc.ui.activity.DetailActivity;
import com.github.tvbox.osc.ui.activity.LocalDataActivity;
import com.github.tvbox.osc.ui.adapter.GridAdapter;
import com.github.tvbox.osc.util.FastClickCheckUtil;
import com.github.tvbox.osc.util.HawkConfig;
import com.github.tvbox.osc.util.LocalDataHelper;
import com.github.tvbox.osc.util.LocalDataLoader;
import com.orhanobut.hawk.Hawk;
import com.owen.tvrecyclerview.widget.TvRecyclerView;
import com.owen.tvrecyclerview.widget.V7GridLayoutManager;

import java.util.ArrayList;
import java.util.List;

public class LocalDataFragment extends BaseLazyFragment {
    private TvRecyclerView mGridView;
    private GridAdapter gridAdapter;
    private List<Movie.Video> localVideoList = new ArrayList<>();

    public static LocalDataFragment newInstance() {
        return new LocalDataFragment();
    }

    @Override
    protected int getLayoutResID() {
        return R.layout.fragment_local_data;
    }

    @Override
    protected void init() {
        initView();
        loadLocalData();
    }

    private void initView() {
        mGridView = findViewById(R.id.mGridView);
        
        gridAdapter = new GridAdapter(false, null);
        mGridView.setAdapter(gridAdapter);
        
        int spanCount = 5;
        mGridView.setLayoutManager(new V7GridLayoutManager(mContext, spanCount));
        
        gridAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                FastClickCheckUtil.check(view);
                Movie.Video video = gridAdapter.getData().get(position);
                if (video != null) {
                    if (video.id.equals("local_data_setting")) {
                        // 跳转到设置页面
                        jumpActivity(LocalDataActivity.class);
                    } else {
                        // 跳转到详情页面
                        Bundle bundle = new Bundle();
                        bundle.putString("id", video.id);
                        bundle.putString("sourceKey", "local");
                        bundle.putString("title", video.name);
                        bundle.putString("picture", video.pic);
                        jumpActivity(DetailActivity.class, bundle);
                    }
                }
            }
        });
    }

    private void loadLocalData() {
        String localPath = Hawk.get(HawkConfig.LOCAL_DATA_PATH, "");
        
        if (localPath.isEmpty()) {
            // 显示设置按钮
            showSettingButton();
        } else {
            // 加载本地数据
            loadLocalVideos(localPath);
        }
    }

    private void showSettingButton() {
        localVideoList.clear();
        
        Movie.Video settingVideo = new Movie.Video();
        settingVideo.id = "local_data_setting";
        settingVideo.name = "点击设置本地数据目录";
        settingVideo.pic = "";
        settingVideo.note = "设置";
        
        localVideoList.add(settingVideo);
        gridAdapter.setNewData(localVideoList);
    }

    private void loadLocalVideos(String path) {
        localVideoList.clear();

        // 添加设置按钮
        Movie.Video settingVideo = new Movie.Video();
        settingVideo.id = "local_data_setting";
        settingVideo.name = "本地数据设置";
        settingVideo.pic = "";
        settingVideo.note = "设置";
        localVideoList.add(settingVideo);

        // 先显示设置按钮
        gridAdapter.setNewData(localVideoList);

        // 使用异步加载工具类
        LocalDataLoader.getInstance().loadLocalVideos(path, getContext(), new LocalDataLoader.LoadCallback() {
            @Override
            public void onSuccess(List<Movie.Video> videos) {
                if (getActivity() != null && !getActivity().isFinishing()) {
                    localVideoList.addAll(videos);
                    gridAdapter.notifyDataSetChanged();

                    if (videos.isEmpty()) {
                        Toast.makeText(mContext, "在指定目录中未找到影片", Toast.LENGTH_SHORT).show();
                    }
                }
            }

            @Override
            public void onError(String error) {
                if (getActivity() != null && !getActivity().isFinishing()) {
                    Toast.makeText(mContext, "加载本地数据失败: " + error, Toast.LENGTH_SHORT).show();
                }
            }
        });
    }

    private String lastLoadedPath = "";

    @Override
    public void onResume() {
        super.onResume();
        // 只有路径发生变化时才重新加载数据
        String currentPath = Hawk.get(HawkConfig.LOCAL_DATA_PATH, "");
        if (!currentPath.equals(lastLoadedPath)) {
            loadLocalData();
            lastLoadedPath = currentPath;
        }
    }
} 