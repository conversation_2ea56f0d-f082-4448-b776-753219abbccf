package com.github.tvbox.osc.util;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import com.github.tvbox.osc.bean.Movie;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 本地数据异步加载工具类
 * 用于在后台线程加载本地影片数据，避免主线程阻塞
 */
public class LocalDataLoader {
    
    private static final String TAG = "LocalDataLoader";
    
    // 单例实例
    private static LocalDataLoader instance;
    
    // 线程池，用于执行后台任务
    private final ThreadPoolExecutor executor;
    
    // 主线程Handler，用于回调到主线程
    private final Handler mainHandler;
    
    private LocalDataLoader() {
        // 使用单线程池，避免并发文件访问问题
        executor = (ThreadPoolExecutor) Executors.newSingleThreadExecutor(r -> {
            Thread thread = new Thread(r, "LocalDataLoader");
            thread.setPriority(Thread.NORM_PRIORITY - 1); // 降低优先级
            return thread;
        });
        mainHandler = new Handler(Looper.getMainLooper());
    }
    
    public static synchronized LocalDataLoader getInstance() {
        if (instance == null) {
            instance = new LocalDataLoader();
        }
        return instance;
    }
    
    /**
     * 异步加载本地视频数据
     * @param rootPath 根目录路径
     * @param context 上下文
     * @param callback 回调接口
     */
    public void loadLocalVideos(String rootPath, Context context, LoadCallback callback) {
        if (TextUtils.isEmpty(rootPath)) {
            if (callback != null) {
                mainHandler.post(() -> callback.onError("路径为空"));
            }
            return;
        }
        
        executor.execute(() -> {
            try {
                // 在后台线程执行文件扫描
                List<Movie.Video> videos = LocalDataHelper.loadLocalVideos(rootPath, context);
                
                // 回到主线程执行回调
                if (callback != null) {
                    mainHandler.post(() -> callback.onSuccess(videos));
                }
            } catch (Exception e) {
                LOG.e(TAG + ": 加载本地视频失败: " + e.getMessage());
                e.printStackTrace();
                
                // 回到主线程执行错误回调
                if (callback != null) {
                    mainHandler.post(() -> callback.onError("加载失败: " + e.getMessage()));
                }
            }
        });
    }
    

    
    /**
     * 取消所有待执行的任务
     */
    public void cancelAll() {
        // 创建新的线程池来替换当前的，这样可以取消所有待执行的任务
        if (!executor.isShutdown()) {
            executor.shutdownNow();
        }
    }
    
    /**
     * 关闭加载器（应用退出时调用）
     */
    public void shutdown() {
        executor.shutdown();
    }
    
    /**
     * 加载回调接口
     */
    public interface LoadCallback {
        /**
         * 加载成功
         * @param videos 视频列表
         */
        void onSuccess(List<Movie.Video> videos);
        
        /**
         * 加载失败
         * @param error 错误信息
         */
        void onError(String error);
    }
    

    
    /**
     * 获取加载器状态信息
     */
    public String getStatus() {
        return String.format("LocalDataLoader: 活跃任务数=%d, 线程池状态=%s",
                executor.getActiveCount(),
                executor.isShutdown() ? "已关闭" : "运行中");
    }
}
