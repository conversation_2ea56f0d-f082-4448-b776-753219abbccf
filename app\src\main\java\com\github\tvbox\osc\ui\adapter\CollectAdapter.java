package com.github.tvbox.osc.ui.adapter;

import android.text.TextUtils;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.github.tvbox.osc.R;
import com.github.tvbox.osc.api.ApiConfig;
import com.github.tvbox.osc.bean.SourceBean;
import com.github.tvbox.osc.cache.VodCollect;
import com.github.tvbox.osc.picasso.RoundTransformation;
import com.github.tvbox.osc.util.DefaultConfig;
import com.github.tvbox.osc.util.HawkConfig;
import com.github.tvbox.osc.util.ImgUtil;
import com.github.tvbox.osc.util.LocalCoverCacheUtil;
import com.github.tvbox.osc.util.MD5;
import com.squareup.picasso.Picasso;

import java.util.ArrayList;
import java.io.File;

import me.jessyan.autosize.utils.AutoSizeUtils;

public class CollectAdapter extends BaseQuickAdapter<VodCollect, BaseViewHolder> {
    public CollectAdapter() {
        super(R.layout.item_grid, new ArrayList<>());
    }

    @Override
    protected void convert(BaseViewHolder helper, VodCollect item) {
    	// takagen99: Add Delete Mode
        FrameLayout tvDel = helper.getView(R.id.delFrameLayout);
        if (HawkConfig.hotVodDelete) {
            tvDel.setVisibility(View.VISIBLE);
        } else {
            tvDel.setVisibility(View.GONE);
        }
        
        helper.setVisible(R.id.tvLang, false);
        helper.setVisible(R.id.tvArea, false);
        helper.setVisible(R.id.tvNote, false);
        helper.setText(R.id.tvName, item.name);
        TextView tvYear = helper.getView(R.id.tvYear);
        SourceBean source = ApiConfig.get().getSource(item.sourceKey);
        tvYear.setText(source!=null?source.getName():"");
        
        ImageView ivThumb = helper.getView(R.id.ivThumb);
        //由于部分电视机使用glide报错
        if (!TextUtils.isEmpty(item.pic)) {
            item.pic = item.pic.trim();
            if (ImgUtil.isBase64Image(item.pic)) {
                // 如果是 Base64 图片，解码并设置
                ivThumb.setImageBitmap(ImgUtil.decodeBase64ToBitmap(item.pic));
            } else if (item.pic.startsWith("file://") && "local".equals(item.sourceKey)) {
                // 本地封面文件，直接使用Picasso加载
                File localFile = new File(item.pic.substring(7)); // 移除 "file://" 前缀
                if (localFile.exists()) {
                    Picasso.get()
                            .load(localFile)
                            .transform(new RoundTransformation(MD5.string2MD5(item.pic))
                                    .centerCorp(true)
                                    .override(AutoSizeUtils.mm2px(mContext, 240), AutoSizeUtils.mm2px(mContext, 336))
                                    .roundRadius(AutoSizeUtils.mm2px(mContext, 10), RoundTransformation.RoundType.ALL))
                            .placeholder(R.drawable.img_loading_placeholder)
                            .noFade()
                            .error(ImgUtil.createTextDrawable(item.name))
                            .into(ivThumb);
                } else {
                    ivThumb.setImageDrawable(ImgUtil.createTextDrawable(item.name));
                }
            } else if (item.pic.startsWith("http") && "local".equals(item.sourceKey)) {
                // 本地影片的网络封面，使用缓存工具类
                String cachedPic = LocalCoverCacheUtil.getCachedCover(item.pic);
                if (!cachedPic.equals(item.pic) && new File(cachedPic).exists()) {
                    // 已缓存到本地文件，使用文件路径加载
                    Picasso.get()
                            .load(new File(cachedPic))
                            .transform(new RoundTransformation(MD5.string2MD5(item.pic))
                                    .centerCorp(true)
                                    .override(AutoSizeUtils.mm2px(mContext, 240), AutoSizeUtils.mm2px(mContext, 336))
                                    .roundRadius(AutoSizeUtils.mm2px(mContext, 10), RoundTransformation.RoundType.ALL))
                            .placeholder(R.drawable.img_loading_placeholder)
                            .noFade()
                            .error(R.drawable.img_loading_placeholder)
                            .into(ivThumb);
                } else {
                    // 还未缓存，先显示占位图，然后异步缓存并更新
                    // ivThumb.setImageDrawable(ImgUtil.createTextDrawable(item.name));
                    final String finalUrl = item.pic;
                    final String finalName = item.name;
                    final ImageView finalIvThumb = ivThumb;
                    LocalCoverCacheUtil.cacheNetworkCover(item.pic, new LocalCoverCacheUtil.CacheCallback() {
                        @Override
                        public void onResult(String result) {
                            if (!result.equals(finalUrl) && new File(result).exists()) {
                                // 缓存完成，更新显示
                                if (finalIvThumb.getContext() instanceof android.app.Activity) {
                                    ((android.app.Activity) finalIvThumb.getContext()).runOnUiThread(() -> {
                                        Picasso.get()
                                                .load(new File(result))
                                                .transform(new RoundTransformation(MD5.string2MD5(finalUrl))
                                                        .centerCorp(true)
                                                        .override(AutoSizeUtils.mm2px(mContext, 240), AutoSizeUtils.mm2px(mContext, 336))
                                                        .roundRadius(AutoSizeUtils.mm2px(mContext, 10), RoundTransformation.RoundType.ALL))
                                                .noFade()
                                                .error(R.drawable.img_loading_placeholder)
                                                .into(finalIvThumb);
                                    });
                                }
                            }
                        }
                    });
                }
            } else {
                Picasso.get()
                        .load(DefaultConfig.checkReplaceProxy(item.pic))
                        .transform(new RoundTransformation(MD5.string2MD5(item.pic))
                                .centerCorp(true)
                                .override(AutoSizeUtils.mm2px(mContext, 240), AutoSizeUtils.mm2px(mContext, 336))
                                .roundRadius(AutoSizeUtils.mm2px(mContext, 10), RoundTransformation.RoundType.ALL))
                        .placeholder(R.drawable.img_loading_placeholder)
                        .noFade()
                        .error(R.drawable.img_loading_placeholder)
                        .into(ivThumb);
            }
        } else {
            ivThumb.setImageResource(R.drawable.img_loading_placeholder);
        }
    }
}